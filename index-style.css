/* Aurora Canvas - Modern Software Solutions Hero */

:root {
    --primary-accent: #AEC3C3;
    --secondary-accent: #5E6666;
    --background-dark: #333737;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Fixed Header */
.aurora-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(174, 195, 195, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 40px;
    width: auto;
    filter: brightness(0.8) contrast(1.1);
    transition: all 0.3s ease;
}

.logo:hover {
    filter: brightness(1) contrast(1.2);
    transform: scale(1.02);
}

.nav-menu {
    display: flex;
    gap: 2.5rem;
    align-items: center;
}

.nav-link {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--secondary-accent);
    text-decoration: none;
    padding: 0.5rem 0;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        rgba(174, 195, 195, 0.8), 
        rgba(94, 102, 102, 0.8), 
        rgba(51, 55, 55, 0.8));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--primary-accent);
    transform: translateY(-1px);
    text-shadow: 0 0 10px rgba(174, 195, 195, 0.3);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-highlight {
    background: rgba(94, 102, 102, 0.3);
    padding: 0.6rem 1.2rem;
    border-radius: 12px;
    border: 1px solid rgba(94, 102, 102, 0.5);
    color: #ffffff;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-highlight:hover {
    background: rgba(94, 102, 102, 0.5);
    border-color: rgba(94, 102, 102, 0.7);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(94, 102, 102, 0.3);
}

.nav-highlight::before {
    display: none;
}

/* Hero Section */
.hero {
    height: 100vh;
    width: 100%;
    background: var(--background-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px; /* Account for fixed header */
}

/* Hero Video Background */
.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
    opacity: 0.6;
    transform: scale(1.2);
    transform-origin: center center;
}

/* Dark overlay for video */
.hero-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 2;
}

/* Hero Content */
.hero-content {
    text-align: center;
    color: #ffffff;
    padding: 3rem 2rem;
    width: 75%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    background: transparent;
    backdrop-filter: blur(5px);
}

.hero-main {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-image-frame {
    flex: 0 0 400px;
    height: 300px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
    background-color: black;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    background-color: black;
}

.hero-text {
    text-align: center;
    padding: 1rem 0;
    max-width: 800px;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-description {
    font-size: 1.2rem;
    color: #ffffff;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-weight: 400;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

/* Hero CTA Buttons */
.hero-cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-block;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    text-align: center;
    min-width: 180px;
    position: relative;
    overflow: hidden;
}

.cta-primary {
    background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(174, 195, 195, 0.3);
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(174, 195, 195, 0.4);
    background: linear-gradient(135deg, var(--secondary-accent), var(--primary-accent));
}

.cta-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.hero-columns {
    display: flex;
    gap: 2rem;
    justify-content: center;
    align-items: stretch;
}

.hero-column {
    flex: 1;
    max-width: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    border-radius: 20px;
    background: var(--primary-accent);
    border: 1px solid rgba(94, 102, 102, 0.2);
    transition: all 0.3s ease;
}

.hero-column:hover {
    background: var(--secondary-accent);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(51, 55, 55, 0.2);
}

.column-image {
    width: 100%;
    margin-bottom: 1.5rem;
}

.image-placeholder {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, 
        rgba(174, 195, 195, 0.3), 
        rgba(94, 102, 102, 0.3));
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.image-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.column-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--background-dark);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.column-description {
    font-size: 0.9rem;
    color: var(--secondary-accent);
    line-height: 1.5;
    margin: 0;
    text-align: center;
}



/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        gap: 1.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-link {
        font-size: 0.9rem;
    }
    
    .nav-highlight {
        padding: 0.5rem 1rem;
    }
    
    .hero-content {
        padding: 1rem;
        max-width: 95%;
    }
    
    .hero-main {
        text-align: center;
    }
    
    .hero-text {
        text-align: center;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-cta-buttons {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        min-width: 160px;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Gradient Text Animation */
.pixelize-text {
    display: inline-block;
    position: relative;
    transition: all 0.3s ease;
    font-weight: 700;
    background: linear-gradient(135deg, 
        var(--background-dark) 0%, 
        var(--primary-accent) 25%, 
        var(--background-dark) 50%, 
        var(--primary-accent) 75%, 
        var(--background-dark) 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 6s ease-in-out infinite;
    text-shadow: 
        0 0 8px rgba(174, 195, 195, 0.2),
        0 0 15px rgba(174, 195, 195, 0.1);
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Focus states for accessibility */
.hero-cta:focus,
.nav-link:focus {
    outline: 2px solid rgba(174, 195, 195, 0.5);
    outline-offset: 2px;
}