<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Section Layout</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* --- Base Styles & Resets --- */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        /* --- Layout Styles --- */
        .product-showcase-section {
            /* Core Requirement: Section takes full viewport height */
            height: 100vh;
            width: 100%;
            
            /* Layout: Using flexbox to center content vertically and horizontally */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            
            /* Visual Aid: Dotted border for the main section container */
            border: 2px dotted #e67e22;
            padding: 2rem;
            position: relative; /* For label positioning */
        }

        .carousel-viewport {
            /* Layout: This container will hold and align the cards */
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem; /* Space between cards */
            width: 100%;
            max-width: 1200px; /* Prevents cards from getting too far apart on large screens */
            
            /* Visual Aid: Dotted border for the carousel area */
            border: 2px dotted #3498db;
            padding: 2rem;
            position: relative;
        }

        .product-card {
            /* Layout: Base styles for all cards */
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            padding: 2rem 1.5rem;
            width: 300px; /* Base width */
            transition: all 0.3s ease-in-out;
            
            /* Visual Aid: Dotted border for each card */
            border: 2px dotted #9b59b6;
            position: relative;
        }

        /* --- Card Variations --- */
        .card-main {
            /* Emphasis: The main card is larger and more prominent */
            transform: scale(1.1);
            z-index: 10;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .card-side {
            /* De-emphasis: Side cards are smaller and slightly faded */
            transform: scale(0.9);
            opacity: 0.7;
            z-index: 5;
        }

        /* --- Content Styles --- */
        .section-title {
            /* Positioning: Placed towards the top, but with margin to account for a potential header */
            margin-bottom: 4rem;
            border: 2px dotted #2ecc71;
            padding: 0.5rem 1rem;
            position: relative;
        }
        
        .card-title, .card-description, .card-list {
             /* Visual Aid: Dotted borders for internal card elements */
            border: 1px dotted #e74c3c;
            position: relative;
            padding: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .card-list ul {
            list-style-position: inside;
            padding-left: 0.5rem;
        }
        
        .card-list li {
            margin-bottom: 0.25rem;
        }

        /* --- Professional Labels --- */
        .layout-label {
            /* Style for the labels to make them clear but unobtrusive */
            position: absolute;
            top: -12px;
            left: 10px;
            background-color: #f8f9fa;
            color: #555;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
            white-space: nowrap;
        }
    </style>
</head>
<body>

    <!-- SECTION: Main container for the entire 100vh component -->
    <section class="product-showcase-section">
        <span class="layout-label">Component: 100vh Section</span>

        <!-- CONTAINER: Section Title -->
        <div class="section-title">
            <span class="layout-label">Element: Section Title</span>
            <h2 style="text-align: center;">Our Featured Products</h2>
        </div>

        <!-- CONTAINER: Carousel Viewport -->
        <!-- This div represents the visible area of the carousel where cards are displayed. -->
        <div class="carousel-viewport">
            <span class="layout-label">Container: Carousel Viewport</span>

            <!-- ITEM: Side Product Card (Left) -->
            <div class="product-card card-side">
                <span class="layout-label">Item: Side Product Card</span>
                <div class="card-title"><h3 style="text-align: center;">Basic Plan</h3></div>
                <div class="card-description"><p>A great starting point for individuals and small teams.</p></div>
                <div class="card-list">
                    <span class="layout-label">Element: List</span>
                    <ul>
                        <li>Feature A</li>
                        <li>Feature B</li>
                        <li>Feature C</li>
                    </ul>
                </div>
            </div>

            <!-- ITEM: Main Product Card (Center) -->
            <div class="product-card card-main">
                <span class="layout-label">Item: Main Product Card</span>
                <div class="card-title"><h3 style="text-align: center;">Pro Plan</h3></div>
                <div class="card-description"><p>The most popular choice for growing businesses and professionals.</p></div>
                <div class="card-list">
                    <span class="layout-label">Element: List</span>
                    <ul>
                        <li>All Basic Features</li>
                        <li>Advanced Feature D</li>
                        <li>Priority Support</li>
                        <li>Integration E</li>
                    </ul>
                </div>
            </div>

            <!-- ITEM: Side Product Card (Right) -->
            <div class="product-card card-side">
                <span class="layout-label">Item: Side Product Card</span>
                <div class="card-title"><h3 style="text-align: center;">Enterprise Plan</h3></div>
                <div class="card-description"><p>For large organizations with custom needs and security requirements.</p></div>
                <div class="card-list">
                    <span class="layout-label">Element: List</span>
                    <ul>
                        <li>All Pro Features</li>
                        <li>Feature F</li>
                        <li>Feature G</li>
                    </ul>
                </div>
            </div>

        </div>

    </section>

</body>
</html>
