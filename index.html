<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aletheia - Software Solutions</title>
    <link rel="stylesheet" href="index-style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>

<!-- Aurora Header -->
<header class="aurora-header">
    <div class="header-container">
        <div class="logo-container">
            <img src="Media/Logo.svg" alt="Aletheia" class="logo">
        </div>
        <nav class="nav-menu">
            <a href="#nosotros" class="nav-link">Nosotros</a>
            <a href="#soluciones" class="nav-link">Soluciones</a>
            <a href="#precios" class="nav-link">Precios</a>
            <a href="#contacto" class="nav-link nav-highlight">Contacto</a>
        </nav>
    </div>
</header>

<!-- Hero Section -->
<section class="hero">
    <video class="hero-video" autoplay muted loop playsinline>
        <source src="Media/Untitled video - Made with Clipchamp.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    <div class="hero-video-overlay"></div>
    <div class="hero-content">
        <div class="hero-main">
            <div class="hero-text">
                <h1 class="hero-title">Soluciones de Software con Inteligencia Artificial</h1>
                <p class="hero-description">Transformamos tu negocio con tecnología de vanguardia que automatiza, optimiza y potencia tus operaciones diarias.</p>
                <div class="hero-cta-buttons">
                    <a href="#contacto" class="cta-button cta-primary">Comenzar Proyecto</a>
                    <a href="#soluciones" class="cta-button cta-secondary">Ver Soluciones</a>
                </div>
            </div>
        </div>
    </div>
</section>



<script>
// Hero Section Interactive Effects
document.addEventListener('DOMContentLoaded', function() {
    const hero = document.querySelector('.hero');
    const heroText = document.querySelector('.hero-text');
    
    // Text fade-in effect
    if (heroText) {
        heroText.style.opacity = '0';
        heroText.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            heroText.style.transition = 'all 0.8s ease';
            heroText.style.opacity = '1';
            heroText.style.transform = 'translateY(0)';
        }, 300);
    }
});
</script>

</body>
</html>